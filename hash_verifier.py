#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import struct
import hashlib
import sys
import os

def check_hash_verification(filename):
    try:
        with open(filename, 'rb') as f:
            data = f.read()
    except Exception as e:
        print(f"文件读取错误: {e}")
        return

    # 提取必要参数
    try:
        payload_size = struct.unpack('<I', data[48:52])[0]
        wrong_check_pos = payload_size + 512
        position_bug_pass = (wrong_check_pos < len(data)) and (data[wrong_check_pos] == 0)
        
        cert_offset_pos = payload_size + 552
        cert_offset = struct.unpack('<Q', data[cert_offset_pos:cert_offset_pos + 8])[0]
    except:
        print("校验参数提取失败")
        return

    # 计算哈希值
    try:
        hash_start = 0x200
        hash_length = payload_size
        if hash_start + hash_length > len(data):
            raise IndexError("哈希计算范围超出文件大小")
            
        # 计算嘻哈值（哈希值）
        calculated_hash = hashlib.sha256(data[hash_start:hash_start + hash_length]).digest()
        
        # 获取预期嘻哈值（哈希值）
        expected_hash = data[cert_offset + 268:cert_offset + 268 + 32]
    except Exception as e:
        print(f"哈希计算错误: {e}")
        return

    # 输出结果
    print(f"计算sha256值: {calculated_hash.hex()}")
    print(f"预期sha256值: {expected_hash.hex()}")
    print(f"检验sha256结果: {'通过' if calculated_hash == expected_hash else '不通过'}")

def main():
    if len(sys.argv) != 2:
        print("用法: python hash_checker.py <文件路径>")
        return 1
    
    input_file = sys.argv[1]
    if not os.path.exists(input_file):
        print(f"文件不存在: {input_file}")
        return 1
    
    check_hash_verification(input_file)
    return 0

if __name__ == "__main__":
    sys.exit(main())