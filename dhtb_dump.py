#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DHTB Dump Tool - Python版本
基于DHTBDump.exe.c源码重写，功能完全相同
用于分析YOCTO Linux splloader分区的DHTB格式文件
"""

import sys
import struct
import os

def print_hex_array(data, prefix=""):
    """打印十六进制数组，格式与原软件一致"""
    hex_str = " ".join(f"{b:x}" for b in data)
    print(f"{prefix}{hex_str}")

def analyze_dhtb_file(filename):
    """
    分析DHTB格式文件
    完全复制DHTBDump.exe的逻辑和输出格式
    """
    try:
        # 读取文件
        with open(filename, 'rb') as f:
            file_data = f.read()
        
        file_size = len(file_data)
        print(f"file size: 0x{file_size:x}")
        
        # 计算payload大小 - 复制原软件逻辑
        payload_size = (file_size + 15) & 0xFFFFFFF0  # 16字节对齐
        
        # 检查魔数 1112819780 (0x42544844 "DHTB")
        if file_size >= 4:
            magic = struct.unpack('<I', file_data[:4])[0]
            if magic == 1112819780:  # DHTB魔数
                # 检查偏移48处的值
                if file_size >= 52:
                    payload_size_from_header = struct.unpack('<I', file_data[48:52])[0]
                    if payload_size_from_header == 0:
                        print("broken sprd trusted firmware")
                        return 1
                    payload_size = payload_size_from_header
        
        print(f"payload size: 0x{payload_size:x}")
        
        # 证书信息位置：payload_size + 544 和 payload_size + 552
        cert_size_offset = payload_size + 544
        cert_offset_offset = payload_size + 552
        
        if file_size < cert_offset_offset + 8:
            print("文件太小，无法包含证书信息")
            return 1
        
        # 读取证书大小和偏移
        cert_size = struct.unpack('<Q', file_data[cert_size_offset:cert_size_offset + 8])[0]
        cert_offset = struct.unpack('<Q', file_data[cert_offset_offset:cert_offset_offset + 8])[0]
        
        print(f"cert size: 0x{cert_size:x}")
        print(f"cert offset: 0x{cert_offset:x}")
        
        # 检查证书偏移是否有效
        if cert_offset >= file_size:
            print("证书偏移超出文件范围")
            return 1
        
        # 读取证书类型
        cert_type = struct.unpack('<I', file_data[cert_offset:cert_offset + 4])[0]
        print(f"cert type: 0x{cert_type:x}")
        
        # 根据证书类型解析不同格式
        if cert_type == 0:
            # Type 0 证书格式
            parse_cert_type_0(file_data, cert_offset)
        elif cert_type == 1:
            # Type 1 证书格式
            parse_cert_type_1(file_data, cert_offset)
        else:
            print(f"未知的证书类型: 0x{cert_type:x}")

        return 0
        
    except FileNotFoundError:
        print(f"文件未找到: {filename}")
        return 1
    except Exception as e:
        print(f"分析文件时出错: {e}")
        return 1

def parse_cert_type_0(file_data, cert_offset):
    """解析Type 0证书格式"""
    # 读取密钥位长度 (offset + 4)
    key_bit_len = struct.unpack('<I', file_data[cert_offset + 4:cert_offset + 8])[0]
    print(f"key bit len: 0x{key_bit_len:x}")

    # 读取e值 (offset + 8)
    e_value = struct.unpack('<I', file_data[cert_offset + 8:cert_offset + 12])[0]
    print(f"e: 0x{e_value:x}")

    # 读取模数 (offset + 12, 256字节)
    mod_data = file_data[cert_offset + 12:cert_offset + 12 + 256]
    print_hex_array(mod_data, "mod: ")

    # 读取数据哈希 (offset + 268, 32字节)
    hash_data = file_data[cert_offset + 268:cert_offset + 268 + 32]
    print_hex_array(hash_data, "hash data:")

    # 读取type和version (offset + 300)
    type_val = struct.unpack('<I', file_data[cert_offset + 300:cert_offset + 304])[0]
    version_val = struct.unpack('<I', file_data[cert_offset + 304:cert_offset + 308])[0]
    print(f"type: 0x{type_val:x}")
    print(f"version: 0x{version_val:x}")

    # 读取签名 (offset + 308, 256字节)
    signature_data = file_data[cert_offset + 308:cert_offset + 308 + 256]
    print_hex_array(signature_data, "signature:")

def parse_cert_type_1(file_data, cert_offset):
    """解析Type 1证书格式"""
    # 读取密钥位长度 (offset + 4)
    key_bit_len = struct.unpack('<I', file_data[cert_offset + 4:cert_offset + 8])[0]
    print(f"key bit len: 0x{key_bit_len:x}")

    # 读取e值 (offset + 8)
    e_value = struct.unpack('<I', file_data[cert_offset + 8:cert_offset + 12])[0]
    print(f"e: 0x{e_value:x}")

    # 读取模数 (offset + 12, 256字节)
    mod_data = file_data[cert_offset + 12:cert_offset + 12 + 256]
    print_hex_array(mod_data, "mod: ")

    # 读取数据哈希 (offset + 268, 32字节)
    hash_data = file_data[cert_offset + 268:cert_offset + 268 + 32]
    print_hex_array(hash_data, "hash data:")

    # 读取密钥哈希 (offset + 300, 32字节)
    hash_key = file_data[cert_offset + 300:cert_offset + 300 + 32]
    print_hex_array(hash_key, "hash key:")

    # 读取type和version (offset + 332)
    type_val = struct.unpack('<I', file_data[cert_offset + 332:cert_offset + 336])[0]
    version_val = struct.unpack('<I', file_data[cert_offset + 336:cert_offset + 340])[0]
    print(f"type: 0x{type_val:x}")
    print(f"version: 0x{version_val:x}")

    # 读取签名 (offset + 340, 256字节)
    signature_data = file_data[cert_offset + 340:cert_offset + 340 + 256]
    print_hex_array(signature_data, "signature:")

def main():
    """主函数"""
    if len(sys.argv) != 2:
        print("用法: python dhtb_dump.py <文件名>")
        print("示例: python dhtb_dump.py uboot")
        return 1
    
    filename = sys.argv[1]
    return analyze_dhtb_file(filename)

if __name__ == "__main__":
    sys.exit(main())
